{"name": "youtubetotext_v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "fix-race-condition": "tsx scripts/fix-race-condition.ts", "test-race-condition": "tsx scripts/test-race-condition.ts"}, "dependencies": {"@mantine/core": "^8.1.2", "@mantine/hooks": "^8.1.2", "@radix-ui/react-slot": "^1.2.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tabler/icons-react": "^3.34.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "daisyui": "^5.0.43", "date-fns": "^4.1.0", "lucide-react": "^0.514.0", "mantine-contextmenu": "^8.1.2", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-resizable-panels": "^3.0.3", "react-spinners": "^0.17.0", "react-wrap-balancer": "^1.1.1", "react-youtube": "^10.1.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.2", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5.8.3"}}