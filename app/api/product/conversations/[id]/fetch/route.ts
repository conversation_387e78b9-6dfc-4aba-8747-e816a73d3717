import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { getAuthContext, validateAccess, matchesConversationOwnership } from '@/lib/auth/apiAuth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  console.log('Fetch API called with conversation ID:', conversationId);

  if (!conversationId) {
    return NextResponse.json(
      { error: 'Conversation ID is required' },
      { status: 400 }
    );
  }

  try {
    // Get authentication context
    const authContext = await getAuthContext(request);

    // Validate access
    const accessValidation = validateAccess(authContext);
    if (!accessValidation.isValid) {
      return NextResponse.json(
        { error: accessValidation.error },
        { status: 401 }
      );
    }

    // Find the conversation by either Supabase ID or Dify conversation ID
    console.log('Looking for conversation with ID:', conversationId);
    const { data: conversation, error: conversationError } = await supabaseAdmin
      .from('conversations')
      .select(`
        id,
        youtube_video_id,
        title,
        dify_transcript_id,
        dify_summary_id,
        transcript,
        user_id,
        session_id,
        is_anonymous
      `)
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .single();

    if (conversationError || !conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (!matchesConversationOwnership(conversation, authContext)) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    const supabaseConversationId = conversation.id;
    console.log('Found conversation with Supabase ID:', supabaseConversationId);

    // Create message filter based on auth context
    const messageFilter = authContext.isAuthenticated
      ? supabaseAdmin.from('messages').select('*').eq('conversation_id', supabaseConversationId).eq('user_id', authContext.userId)
      : supabaseAdmin.from('messages').select('*').eq('conversation_id', supabaseConversationId).eq('session_id', authContext.sessionId);

    // Fetch messages for the conversation
    const { data: messages, error: messagesError } = await messageFilter.order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    // Fetch summary content for the conversation using the actual Supabase ID
    const { data: contentData, error: contentError } = await supabase
      .from('content')
      .select('content')
      .eq('conversation_id', supabaseConversationId)
      .single();

    if (contentError && contentError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching content:', contentError);
      return NextResponse.json(
        { error: 'Failed to fetch summary content' },
        { status: 500 }
      );
    }

    // Extract the content field
    let summaryContent = null;
    if (contentData?.content) {
      // Check if content is already an object or needs to be parsed
      if (typeof contentData.content === 'object') {
        // If it's already an object, access the content field directly
        summaryContent = contentData.content.content || null;
      } else if (typeof contentData.content === 'string') {
        try {
          // If it's a string, try to parse it as JSON
          const parsedContent = JSON.parse(contentData.content);
          summaryContent = parsedContent.content || parsedContent.Content || null;
        } catch (parseError) {
          console.warn('Failed to parse content JSON:', parseError);
          // If parsing fails, use the raw content string
          summaryContent = contentData.content;
        }
      }
    }

    // Extract video metadata from the combined transcript structure
    let videoMetadata = null;
    if (conversation.transcript && conversation.transcript.metadata) {
      videoMetadata = conversation.transcript.metadata;
      console.log('Found video metadata in transcript');
    }

    return NextResponse.json({
      success: true,
      data: {
        messages: messages || [],
        summary: summaryContent,
        conversation: {
          youtube_video_id: conversation.youtube_video_id,
          name: conversation.title,
          video_metadata: videoMetadata || null
        }
      }
    });

  } catch (error) {
    console.error('Error in fetch conversation API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 