#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix the race condition in worker processing
 * This script:
 * 1. Runs the database migration to add the atomic claiming functions
 * 2. Cleans up any stale conversations
 * 3. Tests the new atomic claiming functionality
 */

import { supabaseAdmin } from '../lib/supabase/admin';
import { cleanupStaleConversations } from '../lib/background-processing/worker';
import fs from 'fs';
import path from 'path';

async function runMigration() {
  console.log('🔧 Running race condition fix migration...');
  console.log('⚠️  Note: This script will attempt to run the migration, but you may need to run it manually.');
  console.log('   Manual command: psql -d your_database -f supabase/migrations/20250731000000_fix_worker_race_condition.sql');

  try {
    // Try to add the worker_id column first
    console.log('Adding worker_id column...');
    const { error: columnError } = await supabaseAdmin
      .from('conversations')
      .select('worker_id')
      .limit(1);

    if (columnError && columnError.message.includes('column "worker_id" does not exist')) {
      console.log('❌ worker_id column does not exist. Please run the migration manually:');
      console.log('   psql -d your_database -f supabase/migrations/20250731000000_fix_worker_race_condition.sql');
      return false;
    }

    // Test if the functions exist
    console.log('Testing claim_pending_conversation function...');
    const { error: claimError } = await supabaseAdmin
      .rpc('claim_pending_conversation', { worker_id: 'test' });

    if (claimError && (claimError.message?.includes('function claim_pending_conversation') || claimError.code === '42883')) {
      console.log('❌ claim_pending_conversation function does not exist. Please run the migration manually:');
      console.log('   psql -d your_database -f supabase/migrations/20250731000000_fix_worker_race_condition.sql');
      return false;
    }

    console.log('✅ Migration appears to be applied successfully!');
    return true;
  } catch (error) {
    console.error('❌ Migration check failed:', error);
    console.log('Please run the migration manually:');
    console.log('   psql -d your_database -f supabase/migrations/20250731000000_fix_worker_race_condition.sql');
    return false;
  }
}

async function testAtomicClaiming() {
  console.log('🧪 Testing atomic conversation claiming...');
  
  try {
    // Test the claim_pending_conversation function
    const { data, error } = await supabaseAdmin
      .rpc('claim_pending_conversation', {
        worker_id: 'test-worker'
      });
    
    if (error) {
      console.error('❌ Atomic claiming test failed:', error.message);
      return false;
    }
    
    console.log('✅ Atomic claiming function is working!');
    console.log(`   Claimed conversations: ${data?.length || 0}`);
    
    // If we claimed a conversation, release it back
    if (data && data.length > 0) {
      for (const conv of data) {
        const { error: releaseError } = await supabaseAdmin
          .rpc('release_conversation', {
            conversation_id: conv.id,
            worker_id: 'test-worker'
          });
        
        if (releaseError) {
          console.warn(`⚠️  Failed to release conversation ${conv.id}:`, releaseError.message);
        } else {
          console.log(`✅ Released conversation ${conv.id} back to pending`);
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Atomic claiming test failed:', error);
    return false;
  }
}

async function cleanupStale() {
  console.log('🧹 Cleaning up stale conversations...');
  
  try {
    const cleanedCount = await cleanupStaleConversations(30);
    console.log(`✅ Cleaned up ${cleanedCount} stale conversations`);
    return cleanedCount;
  } catch (error) {
    console.error('❌ Stale cleanup failed:', error);
    return 0;
  }
}

async function checkDatabaseStatus() {
  console.log('📊 Checking database status...');
  
  try {
    // Check for conversations in various states
    const { data: pending, error: pendingError } = await supabaseAdmin
      .from('conversations')
      .select('id, processing_status, processing_started_at, worker_id')
      .eq('processing_status', 'pending');
    
    const { data: processing, error: processingError } = await supabaseAdmin
      .from('conversations')
      .select('id, processing_status, processing_started_at, worker_id')
      .eq('processing_status', 'processing');
    
    if (pendingError || processingError) {
      console.error('❌ Failed to check database status');
      return;
    }
    
    console.log(`📈 Database Status:`);
    console.log(`   Pending conversations: ${pending?.length || 0}`);
    console.log(`   Processing conversations: ${processing?.length || 0}`);
    
    if (processing && processing.length > 0) {
      console.log(`   Processing details:`);
      processing.forEach(conv => {
        const startedAt = conv.processing_started_at ? new Date(conv.processing_started_at) : null;
        const duration = startedAt ? Math.round((Date.now() - startedAt.getTime()) / 1000) : 'unknown';
        console.log(`     - ${conv.id}: worker=${conv.worker_id || 'unknown'}, duration=${duration}s`);
      });
    }
  } catch (error) {
    console.error('❌ Database status check failed:', error);
  }
}

async function main() {
  console.log('🚀 Starting race condition fix process...\n');

  try {
    // Step 1: Check current database status
    await checkDatabaseStatus();
    console.log('');

    // Step 2: Clean up any stale conversations first
    await cleanupStale();
    console.log('');

    // Step 3: Check/run the migration
    const migrationApplied = await runMigration();
    console.log('');

    if (!migrationApplied) {
      console.log('⚠️  Migration needs to be applied manually. Please run:');
      console.log('   psql -d your_database -f supabase/migrations/20250731000000_fix_worker_race_condition.sql');
      console.log('   Then run this script again to test the functionality.');
      return;
    }

    // Step 4: Test the new functionality
    const testPassed = await testAtomicClaiming();
    console.log('');

    // Step 5: Check final database status
    await checkDatabaseStatus();
    console.log('');

    if (testPassed) {
      console.log('🎉 Race condition fix completed successfully!');
      console.log('   Workers will now use atomic conversation claiming to prevent race conditions.');
      console.log('   Restart your application to use the new functionality.');
    } else {
      console.log('⚠️  Race condition fix completed with warnings.');
      console.log('   The migration was applied but testing failed.');
      console.log('   Workers will fall back to the old method if needed.');
    }

  } catch (error) {
    console.error('💥 Race condition fix failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
